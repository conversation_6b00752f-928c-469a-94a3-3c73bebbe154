{"required": true, "minVersion": "0.8", "package": "io.izzel.arclight.common.mixin.core", "target": "@env(DEFAULT)", "refmap": "mixins.arclight.refmap.json", "setSourceFile": true, "plugin": "io.izzel.arclight.common.mod.ArclightMixinPlugin", "injectors": {"maxShiftBy": 2, "defaultRequire": 0}, "overwrites": {"conformVisibility": true}, "mixinPriority": 500, "compatibilityLevel": "JAVA_17", "mixins": ["advancements.AdvancementMixin", "commands.CommandsMixin", "commands.CommandSource1Mixin", "commands.CommandSourceMixin", "commands.CommandSourceStackMixin", "commands.arguments.EntityArgumentMixin", "commands.arguments.blocks.BlockStateParserMixin", "commands.arguments.selector.EntitySelectorParserMixin", "fluid.FlowingFluidMixin", "fluid.LavaFluidMixin", "nbt.CompoundTagMixin", "network.ConnectionMixin", "network.ServerHandshakeNetHandlerMixin", "network.ServerLoginNetHandlerMixin", "network.ServerPlayNetHandlerMixin", "network.ServerStatusNetHandlerMixin", "network.SynchedEntityDataMixin", "network.VelocityCommandArgumentMixin", "network.chat.StyleMixin", "network.chat.TextColorMixin", "network.protocol.PacketThreadUtilMixin", "network.protocol.game.CCloseWindowPacketMixin", "network.protocol.game.ClientboundCommandsPacket_ArgumentNodeStubMixin", "network.protocol.game.ClientboundSectionBlocksUpdatePacketMixin", "network.protocol.game.ClientboundSystemChatPacketMixin", "network.protocol.game.CPlayerTryUseItemOnBlockPacketMixin", "network.protocol.game.CPlayerTryUseItemPacketMixin", "network.protocol.game.SWorldBorderPacketMixin", "network.protocol.handshake.CHandshakePacketMixin", "network.protocol.status.ServerStatusMixin", "network.rcon.RConConsoleSourceMixin", "server.BootstrapMixin", "server.CustomServerBossInfoMixin", "server.Main_ServerShutdownThreadMixin", "server.MinecraftServerMixin", "server.PlayerAdvancementsMixin", "server.ServerFunctionManagerMixin", "server.ServerScoreboardMixin", "server.WorldLoaderMixin", "server.commands.EffectCommandMixin", "server.commands.GameRuleCommandMixin", "server.commands.ReloadCommandMixin", "server.commands.SetSpawnCommandMixin", "server.commands.SummonCommandMixin", "server.commands.TeleportCommandMixin", "server.commands.TimeCommandMixin", "server.dedicated.DedicatedServerMixin", "server.level.ChunkHolderMixin", "server.level.ChunkMap_TrackedEntityMixin", "server.level.ChunkMapMixin", "server.level.DistanceManagerMixin", "server.level.ServerChunkCache_MainThreadExecutorMixin", "server.level.ServerChunkCacheMixin", "server.level.ServerEntityMixin", "server.level.ServerLevel_EntityCallbacksMixin", "server.level.ServerLevelMixin", "server.level.TicketTypeMixin", "server.management.BanEntryMixin", "server.management.DedicatedPlayerListMixin", "server.management.PlayerListMixin", "server.management.ServerPlayerGameModeMixin", "server.management.UserListMixin", "stats.StatisticsCounterMixin", "world.BlockGetterMixin", "world.CompoundContainerMixin", "world.ContainerMixin", "world.ExplosionMixin", "world.IServerWorldMixin", "world.IWorldMixin", "world.IWorldWriterMixin", "world.SimpleContainerMixin", "world.damagesource.DamageSourceMixin", "world.damagesource.DamageSourcesMixin", "world.effect.MobEffectMixin", "world.effect.MobEffectUtilMixin", "world.entity.AgeableMobMixin", "world.entity.AreaEffectCloudEntityMixin", "world.entity.EntityMixin", "world.entity.EntityTypeMixin", "world.entity.ExperienceOrbMixin", "world.entity.IAngerableMixin", "world.entity.InteractionMixin", "world.entity.ItemBaseSteeringMixin", "world.entity.LightningBoltMixin", "world.entity.LivingEntityMixin", "world.entity.LivingEntityMixin$ApotheosisCompatMixin", "world.entity.LivingEntityMixin$ObscureApiCompat", "world.entity.MobMixin", "world.entity.PathfinderMobMixin", "world.entity.ai.behavior.AssignProfessionFromJobSiteMixin", "world.entity.ai.behavior.BabyFollowAdultMixin", "world.entity.ai.behavior.GoToWantedItemMixin", "world.entity.ai.behavior.HarvestFarmlandMixin", "world.entity.ai.behavior.InteractWithDoorMixin", "world.entity.ai.behavior.PrepareRamNearestTargetMixin", "world.entity.ai.behavior.ResetProfessionMixin", "world.entity.ai.behavior.StartAttackingMixin", "world.entity.ai.behavior.StopAttackingIfTargetInvalidMixin", "world.entity.ai.behavior.VillagerMakeLoveMixin", "world.entity.ai.brain.BrainUtilMixin", "world.entity.ai.goal.BreakDoorGoalMixin", "world.entity.ai.goal.DefendVillageTargetGoalMixin", "world.entity.ai.goal.EatBlockGoalMixin", "world.entity.ai.goal.FollowOwnerGoalMixin", "world.entity.ai.goal.HurtByTargetGoalMixin", "world.entity.ai.goal.NearestAttackableTargetGoalMixin", "world.entity.ai.goal.OwnerHurtByTargetGoalMixin", "world.entity.ai.goal.OwnerHurtTargetGoalMixin", "world.entity.ai.goal.RemoveBlockGoalMixin", "world.entity.ai.goal.SkeletonTrapGoalMixin", "world.entity.ai.goal.TargetGoalMixin", "world.entity.ai.goal.TemptGoalMixin", "world.entity.ai.sensing.TemptingSensorMixin", "world.entity.ai.village.VillageSiegeMixin", "world.entity.ambient.BatMixin", "world.entity.animal.AllayMixin", "world.entity.animal.AnimalMixin", "world.entity.animal.Bee_GrowCropGoalMixin", "world.entity.animal.Bee_HurtByOtherGoalMixin", "world.entity.animal.BeeMixin", "world.entity.animal.BucketableMixin", "world.entity.animal.Cat_CatRelaxOnOwnerGoalMixin", "world.entity.animal.ChickenMixin", "world.entity.animal.CowMixin", "world.entity.animal.DolphinEntity_SwimWithPlayerGoalMixin", "world.entity.animal.DolphinMixin", "world.entity.animal.Fox_BreedGoalMixin", "world.entity.animal.Fox_EatBerriesGoalMixin", "world.entity.animal.FoxMixin", "world.entity.animal.IronGolemMixin", "world.entity.animal.MushroomCowMixin", "world.entity.animal.OcelotMixin", "world.entity.animal.Panda_HurtByTargetGoalMixin", "world.entity.animal.PandaMixin", "world.entity.animal.ParrotMixin", "world.entity.animal.PigMixin", "world.entity.animal.PufferfishEntityMixin", "world.entity.animal.Rabbit_RaidGardenGoalMixin", "world.entity.animal.RabbitMixin", "world.entity.animal.Sheep1Mixin", "world.entity.animal.SheepMixin", "world.entity.animal.SnifferMixin", "world.entity.animal.SnowGolemMixin", "world.entity.animal.TameableAnimalMixin", "world.entity.animal.Turtle_LayEggGoalMixin", "world.entity.animal.TurtleMixin", "world.entity.animal.WolfMixin", "world.entity.animal.axolotl.AxolotlMixin", "world.entity.animal.frog.TadpoleMixin", "world.entity.animal.goat.GoatMixin", "world.entity.animal.horse.AbstractHorseMixin", "world.entity.animal.horse.LlamaMixin", "world.entity.animal.horse.TraderLlamaEntity_FollowTraderGoalMixin", "world.entity.boss.enderdragon.EnderCrystalMixin", "world.entity.boss.enderdragon.EnderDragonMixin", "world.entity.boss.enderdragon.phases.EnderDragonPhaseManagerMixin", "world.entity.boss.wither.WitherBossMixin", "world.entity.decoration.ArmorStandMixin", "world.entity.decoration.ItemFrameMixin", "world.entity.decoration.LeashFenceKnotEntityMixin", "world.entity.item.FallingBlockEntityMixin", "world.entity.item.HangingEntityMixin", "world.entity.item.ItemEntityMixin", "world.entity.item.PrimedTntMixin", "world.entity.monster.AbstractSkeletonMixin", "world.entity.monster.CaveSpiderMixin", "world.entity.monster.CreeperMixin", "world.entity.monster.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "world.entity.monster.EnderMan_EndermanLeaveBlockGoalMixin", "world.entity.monster.EnderMan_EndermanTakeBlockGoalMixin", "world.entity.monster.EnderManMixin", "world.entity.monster.Evoker_EvokerSummonSpellGoalMixin", "world.entity.monster.Ghast_GhastShootFireballGoalMixin", "world.entity.monster.GuardianMixin", "world.entity.monster.HuskMixin", "world.entity.monster.Illusioner_BlindnessSpellGoalMixin", "world.entity.monster.Illusioner_MirrorSpellGoalMixin", "world.entity.monster.Phantom_AttackPlayerTargetGoalMixin", "world.entity.monster.RavagerMixin", "world.entity.monster.ShulkerMixin", "world.entity.monster.Silverfish_MergeWithStoneGoalMixin", "world.entity.monster.Silverfish_WakeUpFriendsGoalMixin", "world.entity.monster.SlimeMixin", "world.entity.monster.SpellcastingIllager_UseSpellGoalMixin", "world.entity.monster.SpiderMixin", "world.entity.monster.StriderMixin", "world.entity.monster.Vex_CopyOwnerTargetGoalMixin", "world.entity.monster.WitchMixin", "world.entity.monster.WitherSkeletonMixin", "world.entity.monster.ZombieMixin", "world.entity.monster.ZombieVillagerMixin", "world.entity.monster.ZombifiedPiglinMixin", "world.entity.monster.piglin.PiglinAiMixin", "world.entity.monster.piglin.PiglinMixin", "world.entity.monster.warden.<PERSON><PERSON><PERSON><PERSON>", "world.entity.npc.AbstractVillagerMixin", "world.entity.npc.InventoryCarrierMixin", "world.entity.npc.VillagerMixin", "world.entity.npc.WanderingTraderMixin", "world.entity.player.InventoryMixin", "world.entity.player.PlayerMixin", "world.entity.player.ServerPlayerMixin", "world.entity.projectile.AbstractArrowMixin", "world.entity.projectile.AbstractHurtingProjectileMixin", "world.entity.projectile.ArrowEntityMixin", "world.entity.projectile.EvokerFangsMixin", "world.entity.projectile.FireballMixin", "world.entity.projectile.FireworkRocketEntityMixin", "world.entity.projectile.FishingHookMixin", "world.entity.projectile.LargeFireballMixin", "world.entity.projectile.ProjectileMixin", "world.entity.projectile.ShulkerBulletMixin", "world.entity.projectile.SmallFireballMixin", "world.entity.projectile.SpectralArrowMixin", "world.entity.projectile.ThrowableItemProjectileMixin", "world.entity.projectile.ThrowableProjectileMixin", "world.entity.projectile.ThrownEggMixin", "world.entity.projectile.ThrownEnderpearlMixin", "world.entity.projectile.ThrownExperienceBottleMixin", "world.entity.projectile.ThrownPotionMixin", "world.entity.projectile.ThrownTridentMixin", "world.entity.projectile.WitherSkullMixin", "world.entity.raid.RaidManagerMixin", "world.entity.raid.RaidMixin", "world.entity.raider.Raider_HoldGroundAttackGoalMixin", "world.entity.raider.RaiderMixin", "world.entity.vehicle.AbstractMinecartContainerMixin", "world.entity.vehicle.AbstractMinecartMixin", "world.entity.vehicle.BoatMixin", "world.entity.vehicle.ChestBoatMixin", "world.entity.vehicle.MinecartCommandBlock_MinecartCommandBaseMixin", "world.entity.vehicle.MinecartTNTMixin", "world.food.FoodDataMixin", "world.gen.WorldGenRegionMixin", "world.gen.feature.structure.SwampHutPieceMixin", "world.inventory.AbstractContainerMenuMixin", "world.inventory.AbstractFurnaceContainerMixin", "world.inventory.BeaconContainerMixin", "world.inventory.BrewingStandContainerMixin", "world.inventory.CartographyContainer1Mixin", "world.inventory.CartographyContainer2Mixin", "world.inventory.CartographyContainerMixin", "world.inventory.ChestContainerMixin", "world.inventory.ContainerLevelAccessMixin", "world.inventory.ContainerTypeMixin", "world.inventory.CraftingInventoryMixin", "world.inventory.CraftingMenuMixin", "world.inventory.CraftResultInventoryMixin", "world.inventory.DispenserContainerMixin", "world.inventory.EnchantmentContainer1Mixin", "world.inventory.EnchantmentContainerMixin", "world.inventory.EnderChestInventoryMixin", "world.inventory.FurnaceResultSlotMixin", "world.inventory.GrindstoneContainer1Mixin", "world.inventory.GrindstoneContainerMixin", "world.inventory.HopperContainerMixin", "world.inventory.HorseInventoryContainerMixin", "world.inventory.ItemCombinerMixin", "world.inventory.LecternContainerMixin", "world.inventory.LoomContainer1Mixin", "world.inventory.LoomContainer2Mixin", "world.inventory.LoomContainerMixin", "world.inventory.MerchantContainerMixin", "world.inventory.MerchantInventoryMixin", "world.inventory.PlayerContainerMixin", "world.inventory.RepairContainerMixin", "world.inventory.ShulkerBoxContainerMixin", "world.inventory.SlotMixin", "world.inventory.SmithingTableContainerMixin", "world.inventory.StonecutterContainer1Mixin", "world.inventory.StonecutterContainerMixin", "world.item.ArmorStandItemMixin", "world.item.BlockItemMixin", "world.item.BoatItemMixin", "world.item.BowItemMixin", "world.item.BucketItemMixin", "world.item.ChorusFruitItemMixin", "world.item.CrossbowItemMixin", "world.item.DyeItemMixin", "world.item.EggItemMixin", "world.item.EnderCrystalItemMixin", "world.item.EnderEyeItemMixin", "world.item.EnderPearlItemMixin", "world.item.FireChargeItemMixin", "world.item.FishingRodItemMixin", "world.item.FlintAndSteelItemMixin", "world.item.HangingEntityItemMixin", "world.item.ItemStackMixin", "world.item.LeadItemMixin", "world.item.MapItemMixin", "world.item.MerchantMixin", "world.item.MerchantOfferMixin", "world.item.MilkBucketItemMixin", "world.item.MinecartItemMixin", "world.item.PotionItemMixin", "world.item.ShearsItemMixin", "world.item.SnowballItemMixin", "world.item.SpawnEggItemMixin", "world.item.StandingAndWallBlockItemMixin", "world.item.TridentItemMixin", "world.item.crafting.BlastingRecipeMixin", "world.item.crafting.CampfireCookingRecipeMixin", "world.item.crafting.CustomRecipeMixin", "world.item.crafting.IngredientMixin", "world.item.crafting.RecipeManagerMixin", "world.item.crafting.RecipeMixin", "world.item.crafting.ServerRecipeBookMixin", "world.item.crafting.ShapedRecipeMixin", "world.item.crafting.ShapelessRecipeMixin", "world.item.crafting.SmeltingRecipeMixin", "world.item.crafting.SmithingTransformRecipeMixin", "world.item.crafting.SmithingTrimRecipeMixin", "world.item.crafting.SmokingRecipeMixin", "world.item.crafting.StonecuttingRecipeMixin", "world.item.enchantment.DamageEnchantmentMixin", "world.item.enchantment.FrostWalkerEnchantmentMixin", "world.level.LevelMixin", "world.level.block.BambooSaplingBlockMixin", "world.level.block.BambooStalkBlockMixin", "world.level.block.BaseFireBlockMixin", "world.level.block.BasePressurePlateBlockMixin", "world.level.block.BedBlockMixin", "world.level.block.BeehiveBlockMixin", "world.level.block.BellBlockMixin", "world.level.block.BigDripleafBlockMixin", "world.level.block.BlockMixin", "world.level.block.BuddingAmethystBlockMixin", "world.level.block.BushBlockMixin", "world.level.block.ButtonBlockMixin", "world.level.block.CactusBlockMixin", "world.level.block.CakeBlockMixin", "world.level.block.CampfireBlockMixin", "world.level.block.CarvedPumpkinBlockMixin", "world.level.block.CauldronBlockMixin", "world.level.block.CaveVinesMixin", "world.level.block.ChangeOverTimeBlockMixin", "world.level.block.ChestBlock2_1Mixin", "world.level.block.ChestBlockMixin", "world.level.block.ChorusFlowerBlockMixin", "world.level.block.CocoaBlockMixin", "world.level.block.CommandBlockMixin", "world.level.block.ComparatorBlockMixin", "world.level.block.ComposterBlock_EmptyContainerMixin", "world.level.block.ComposterBlock_InputContainerMixin", "world.level.block.ComposterBlock_OutputContainerMixin", "world.level.block.ComposterBlockMixin", "world.level.block.ConcretePowderBlockMixin", "world.level.block.CoralBlockMixin", "world.level.block.CoralFanBlockMixin", "world.level.block.CoralPlantBlockMixin", "world.level.block.CoralWallFanBlockMixin", "world.level.block.CropBlockMixin", "world.level.block.DaylightDetectorBlockMixin", "world.level.block.DetectorRailBlockMixin", "world.level.block.DiodeBlockMixin", "world.level.block.DirtPathBlockMixin", "world.level.block.DispenserBlockMixin_Accessor", "world.level.block.DoorBlockMixin", "world.level.block.DoublePlantBlockMixin", "world.level.block.DragonEggBlockMixin", "world.level.block.DropperBlockMixin", "world.level.block.EndPortalBlockMixin", "world.level.block.FallingBlockMixin", "world.level.block.FarmBlockMixin", "world.level.block.FenceGateBlockMixin", "world.level.block.FireBlockMixin", "world.level.block.FungusBlockMixin", "world.level.block.GrowingPlantHeadBlockMixin", "world.level.block.IceBlockMixin", "world.level.block.InfestedBlockMixin", "world.level.block.LayeredCauldronBlockMixin", "world.level.block.LeavesBlockMixin", "world.level.block.LecternBlockMixin", "world.level.block.LeverBlockMixin", "world.level.block.LightningRodBlockMixin", "world.level.block.LilyPadBlockMixin", "world.level.block.LiquidBlockMixin", "world.level.block.MagmaBlockMixin", "world.level.block.MultifaceSpreader_SpreadConfigMixin", "world.level.block.MultifaceSpreaderMixin", "world.level.block.MushroomBlockMixin", "world.level.block.NetherPortalBlockMixin", "world.level.block.NetherWartBlockMixin", "world.level.block.NoteBlockMixin", "world.level.block.NyliumBlockMixin", "world.level.block.ObserverBlockMixin", "world.level.block.PistonBlockMixin", "world.level.block.PointedDripstoneBlockMixin", "world.level.block.PortalInfoMixin", "world.level.block.PortalShapeMixin", "world.level.block.PowderSnowBlockMixin", "world.level.block.PoweredRailBlockMixin", "world.level.block.PressurePlateBlockMixin", "world.level.block.RedstoneLampBlockMixin", "world.level.block.RedstoneOreBlockMixin", "world.level.block.RedstoneTorchBlockMixin", "world.level.block.RedstoneWireBlockMixin", "world.level.block.RespawnAnchorBlockMixin", "world.level.block.RootedDirtBlockMixin", "world.level.block.SaplingBlockMixin", "world.level.block.ScaffoldingBlockMixin", "world.level.block.SculkBlockMixin", "world.level.block.SculkSensorBlockMixin", "world.level.block.SculkShriekerBlockMixin", "world.level.block.SculkSpreaderMixin", "world.level.block.SculkVeinBlockMixin", "world.level.block.SignBlockMixin", "world.level.block.SnowBlockMixin", "world.level.block.SnowLayerBlockMixin", "world.level.block.SpongeBlockMixin", "world.level.block.SpreadableSnowyDirtBlockMixin", "world.level.block.StemBlockMixin", "world.level.block.SugarCaneBlockMixin", "world.level.block.SweetBerryBushBlockMixin", "world.level.block.TntBlockMixin", "world.level.block.TrapDoorBlockMixin", "world.level.block.TripWireBlockMixin", "world.level.block.TripWireHookBlockMixin", "world.level.block.TurtleEggBlockMixin", "world.level.block.VineBlockMixin", "world.level.block.WeightedPressurePlateBlockMixin", "world.level.block.WitherRoseBlockMixin", "world.level.block.WitherSkullBlockMixin", "world.level.block.entity.AbstractFurnaceBlockEntityMixin", "world.level.block.entity.BarrelBlockEntityMixin", "world.level.block.entity.BeaconTileEntityMixin", "world.level.block.entity.BeehiveBlockEntityMixin", "world.level.block.entity.BellBlockEntityMixin", "world.level.block.entity.BlockEntityMixin", "world.level.block.entity.BrewingStandBlockEntityMixin", "world.level.block.entity.BrushableBlockEntityMixin", "world.level.block.entity.CampfireBlockEntityMixin", "world.level.block.entity.ChestBlockEntityMixin", "world.level.block.entity.ChiseledBookShelfBlockEntityMixin", "world.level.block.entity.CommandBlockLogicMixin", "world.level.block.entity.CommandBlockTileEntity1Mixin", "world.level.block.entity.ConduitBlockEntityMixin", "world.level.block.entity.ContainerOpenersCounterMixin", "world.level.block.entity.DispenserBlockEntityMixin", "world.level.block.entity.EndGatewayBlockEntityMixin", "world.level.block.entity.HopperBlockEntityMixin", "world.level.block.entity.JukeboxBlockEntityMixin", "world.level.block.entity.LecternBlockEntityMixin", "world.level.block.entity.LecternTileEntity1Mixin", "world.level.block.entity.LockableBlockEntityMixin", "world.level.block.entity.SculkCatalystBlockEntity_CatalystListenerMixin", "world.level.block.entity.SculkCatalystBlockEntityMixin", "world.level.block.entity.ShulkerBoxBlockEntityMixin", "world.level.block.entity.SignBlockEntityMixin", "world.level.block.entity.SoulSurgeBlockEntityMixin", "world.level.block.state.BlockBehaviour_BlockStateBaseMixin", "world.level.block.state.BlockBehaviourMixin", "world.level.border.WorldBorderMixin", "world.level.chunk.ChunkAccessMixin", "world.level.chunk.ChunkGeneratorMixin", "world.level.chunk.LevelChunk_BoundTickingBlockEntityMixin", "world.level.chunk.LevelChunkMixin", "world.level.chunk.LevelChunkSectionMixin", "world.level.chunk.storage.ChunkLoaderMixin", "world.level.chunk.storage.ChunkSerializerMixin", "world.level.chunk.storage.RegionFileCacheMixin", "world.level.entity.PersistentEntitySectionManagerMixin", "world.level.gameevent.GameEventDispatcherMixin", "world.level.gameevent.vibrations.VibrationListenerMixin", "world.level.levelgen.structure.templatesystem.StructurePlaceSettingsMixin", "world.level.levelgen.structure.templatesystem.StructureTemplateMixin", "world.level.portal.PortalForcerMixin", "world.level.redstone.NeighborUpdaterMixin", "world.level.saveddata.maps.MapDataMixin", "world.level.saveddata.maps.MapItemSavedData_HoldingPlayerMixin", "world.level.storage.loot.LootDataManagerMixin", "world.level.storage.loot.LootTableMixin", "world.level.storage.loot.entries.LootEntry_SerializerMixin", "world.level.storage.loot.functions.LootingEnchantBonusMixin", "world.level.storage.loot.parameters.LootParametersMixin", "world.level.storage.loot.predicates.RandomChanceWithLootingMixin", "world.level.storage.loot.predicates.SurvivesExplosionMixin", "world.spawner.BaseSpawnerMixin", "world.spawner.NaturalSpawnerMixin", "world.spawner.PatrolSpawnerMixin", "world.spawner.PhantomSpawnerMixin", "world.spawner.WanderingTraderSpawnerMixin", "world.spawner.WorldEntitySpawner_EntityDensityManagerMixin", "world.storage.DerivedWorldInfoMixin", "world.storage.LevelStorageSource_LevelStorageAccessMixin", "world.storage.LevelStorageSourceMixin", "world.storage.PlayerDataMixin", "world.storage.PrimaryLevelDataMixin"]}