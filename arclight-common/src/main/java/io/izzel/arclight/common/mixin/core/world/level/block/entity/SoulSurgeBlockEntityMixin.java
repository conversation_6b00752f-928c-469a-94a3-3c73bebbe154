package io.izzel.arclight.common.mixin.core.world.level.block.entity;

import io.izzel.arclight.common.mod.compat.ModIds;
import io.izzel.arclight.common.mod.mixins.annotation.LoadIfMod;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(targets = "com.buuz135.industrialforegoingsouls.block.tile.SoulSurgeBlockEntity")
@LoadIfMod(modid = {ModIds.INDUSTRIAL_FOREGOING_SOULS}, condition = LoadIfMod.ModCondition.PRESENT)
public class SoulSurgeBlockEntityMixin {

    @Inject(method = "serverTick", at = @At("HEAD"), cancellable = true, remap = false)
    private void arclight$nullCheck(CallbackInfo ci) {
        try {
            // Use reflection to check if network field is null
            var networkField = this.getClass().getDeclaredField("network");
            networkField.setAccessible(true);
            var network = networkField.get(this);
            if (network == null) {
                // Cancel the tick if network is null to prevent NPE
                ci.cancel();
            }
        } catch (Exception e) {
            // If reflection fails, let the original method run
        }
    }
}
