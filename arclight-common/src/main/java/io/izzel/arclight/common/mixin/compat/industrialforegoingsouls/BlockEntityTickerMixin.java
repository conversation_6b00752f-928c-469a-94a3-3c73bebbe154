package io.izzel.arclight.common.mixin.compat.industrialforegoingsouls;

import io.izzel.arclight.common.mod.compat.ModIds;
import io.izzel.arclight.common.mod.mixins.annotation.LoadIfMod;
import io.izzel.arclight.common.mod.util.log.ArclightI18nLogger;
import net.minecraft.core.BlockPos;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.level.block.entity.BlockEntityTicker;
import net.minecraft.world.level.block.state.BlockState;
import org.apache.logging.log4j.Logger;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

/**
 * Fix for Industrial Foregoing Souls BlockEntity NPE
 * Prevents server crash when Industrial Foregoing Souls BlockEntities throw NPE during tick
 */
@Mixin(BlockEntityTicker.class)
@LoadIfMod(modid = {ModIds.INDUSTRIAL_FOREGOING_SOULS}, condition = LoadIfMod.ModCondition.PRESENT)
public class BlockEntityTickerMixin {

    private static final Logger LOGGER = ArclightI18nLogger.getLogger("Luminara-IFSCompat");

    /**
     * Wrap BlockEntityTicker.tick() with exception handling for Industrial Foregoing Souls
     * This prevents server crashes from NPE in Industrial Foregoing Souls BlockEntities
     */
    @Inject(method = "tick", at = @At("HEAD"), cancellable = true)
    private static <T extends BlockEntity> void luminara$safeTickIndustrialForegoingSouls(
            Level level, BlockPos pos, BlockState state, T blockEntity, CallbackInfo ci) {
        
        // Check if this is an Industrial Foregoing Souls BlockEntity
        String className = blockEntity.getClass().getName();
        if (className.startsWith("com.buuz135.industrialforegoingsouls.")) {
            try {
                // Let the original method run, but catch any exceptions
                return;
            } catch (NullPointerException e) {
                // Check if this is the specific network NPE we're trying to fix
                String message = e.getMessage();
                if (message != null && message.contains("getSoulLaserDrills") && message.contains("network")) {
                    LOGGER.warn("Prevented Industrial Foregoing Souls crash: {} at {} - network field is null, skipping tick", 
                               className, pos);
                    ci.cancel();
                    return;
                }
                // Re-throw if it's a different NPE
                throw e;
            } catch (Exception e) {
                // Log other exceptions but don't crash the server
                LOGGER.error("Error in Industrial Foregoing Souls BlockEntity {} at {}: {}", 
                            className, pos, e.getMessage());
                ci.cancel();
            }
        }
    }
}
